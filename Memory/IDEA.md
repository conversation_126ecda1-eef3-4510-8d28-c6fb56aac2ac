# 项目纲要：动态任务图谱差分更新实验 (IDEA.md)

> **核心原则**: 本文档是本项目的最高指导原则，定义了实验目标、方法、环境及规范。所有 AI 生成的代码、分析和报告都必须严格遵守本文档的规定。





## 1. 数据集

所有原始帧级特征（`/data2/syd_data/Breakfast_Data/breakfast_data/` 下 `s1–s4/任务/*.txt`）与对应段级标签（`/data2/syd_data/Breakfast_Data/segmentation_coarse/` 下同结构的 `*.txt`）都需**重新**转换为 `.npy` 并覆盖旧文件，为此请在 `/data2/syd_data/Breakfast_Data/Code/` 新建 **`txt_to_npy.py`**。

脚本流程与约束如下：

① 递归扫描全部 `.txt`；

② 逐文件读取后，将首列视为帧索引、其余列为特征；

③ 若同一时间戳存在 1–4 个视角，则先按视角拼接后对同维度元素取平均（缺失视角以零向量补齐）得到单一融合特征；若不同视角帧数不一致，仅保留所有视角同步存在的帧；融合后输出固定 64 维向量。

④ 将结果保存至 `/data2/syd_data/Breakfast_Data/breakfast_data_npy/**s?/任务/*.npy`，若目标文件已存在直接覆写；

⑤ 转换同时执行三项一致性检查：

- a) 行数应与生成数组第一维完全相等；
- b) 首列帧索引需与 `np.arange(start,start+N)` 等长等值；
- c) 文本与数组在均值、方差上的绝对差 < 1e-6，否则报错中止；检查日志写入同目录 `txt_to_npy.log`；

⑥ 段级标签文本同步转为 `.npy` 保存到 `segmentation_coarse_npy/s?_label/任务/*.npy`，并使用同一 **`label_map.json`**（提供，记录 `{id: 动作名}` 对照表）确保索引一致；label_map.json 固定放在/data2/syd_data/Breakfast_Data/label_map.json

⑦ 数据划分约定：s1-s3 及对应标签用作训练，s4 用作测试；模型训练仅在动作段边界生成样本（即取每段最后一帧特征 `V_k`、当前动作 `n_k` 与下段动作 `n_{k+1}`），忽略段内帧。

脚本完成后，运行一次即可生成（并覆写）所有 `.npy` 文件，满足后续实验对快速加载、视角融合和标签一致性的要求。

原始训练数据格式：训练数据是每一帧的信息都进行了记录，标签中是以段级别进行的标注。

例如：

- /data2/syd_data/Breakfast_Data/breakfast_data/s1/cereals/路径下的原始帧级别数据格式为（5-10帧，第一列是序号，后面64列是实际数据）：

  ```txt
  5.000000 	0.184755 	0.382133 	2.493219 	-1.652432 	2.135418 	5.050336 	-5.612562 	4.427806 	-3.902953 	3.705645 	-0.749006 	-1.172183 	0.824883 	1.773496 	-0.427171 	1.358154 	0.487730 	3.749245 	0.375241 	0.750035 	-0.431291 	2.162578 	0.471532 	1.592205 	-5.854745 	-0.519448 	0.029078 	-2.572175 	4.421773 	2.175261 	-2.967161 	3.531760 	4.125837 	2.532486 	2.447726 	-3.812308 	-5.454903 	2.570730 	-0.658669 	4.791240 	4.452132 	5.848291 	-1.562722 	-1.710355 	2.520462 	-0.232897 	-2.752872 	9.602302 	6.096712 	-1.757259 	-3.056594 	0.482004 	-1.766842 	-0.173561 	1.078081 	-3.490958 	-0.956082 	-3.445616 	-8.269568 	-0.386341 	-3.271580 	5.294833 	0.347500 	-6.191317 
  6.000000 	0.121161 	0.556484 	2.525131 	-1.543501 	2.020302 	5.142955 	-5.612754 	4.533225 	-3.729580 	3.602172 	-0.777629 	-1.148723 	0.940942 	1.664575 	-0.433067 	1.407925 	0.397994 	3.642177 	0.465480 	0.769408 	-0.296944 	2.096348 	0.431659 	1.700171 	-5.868377 	-0.465508 	0.005962 	-2.505919 	4.399172 	2.191113 	-2.996573 	3.409282 	4.139851 	2.612151 	2.426474 	-3.739493 	-5.456091 	2.723189 	-0.651778 	4.712954 	4.543432 	5.699573 	-1.488536 	-1.819674 	2.536882 	-0.317910 	-2.811333 	9.749245 	6.118546 	-1.756847 	-3.221541 	0.668405 	-1.802642 	-0.318426 	1.045020 	-3.650563 	-1.020847 	-3.349360 	-8.257033 	-0.622786 	-3.234530 	5.353365 	0.325645 	-6.213550 
  7.000000 	0.606392 	-0.665494 	2.191830 	-1.903055 	2.251442 	5.733404 	-6.073134 	4.825317 	-3.420153 	3.317323 	-0.991382 	-1.815369 	0.682443 	2.743808 	-0.586435 	1.752120 	0.535006 	3.226093 	0.034021 	1.253477 	-0.510043 	2.173371 	0.878530 	1.301777 	-5.287728 	-0.536347 	0.215694 	-2.358813 	4.330668 	2.512551 	-3.119747 	3.555067 	4.667192 	3.491647 	2.019424 	-4.320953 	-5.457394 	3.949594 	0.334386 	4.879919 	5.469223 	5.776865 	-1.222993 	-3.027710 	2.683572 	0.191388 	-3.887067 	9.223993 	5.859837 	-2.452577 	-2.239301 	0.222606 	-1.608703 	-0.159557 	1.678084 	-4.556506 	-0.587006 	-3.601549 	-7.203865 	-1.828943 	-3.902133 	5.365033 	0.242213 	-6.331494 
  8.000000 	0.689354 	-1.588536 	1.958060 	-2.052399 	2.967427 	5.161489 	-5.024700 	4.999101 	-4.366857 	3.542020 	-0.794785 	-2.278612 	-0.383313 	2.754561 	-0.050932 	1.445578 	-0.313200 	3.785226 	0.130441 	1.234223 	0.696913 	1.599726 	1.137412 	0.900468 	-5.608068 	-0.636176 	0.248218 	-2.343154 	5.016736 	2.215586 	-2.940979 	3.316946 	4.922168 	3.602228 	1.747500 	-4.145300 	-4.901201 	3.198227 	0.932281 	5.187385 	5.626126 	6.036348 	-1.576501 	-3.088832 	2.960864 	1.616781 	-5.072955 	9.875139 	5.784187 	-2.199903 	-1.661192 	-0.106054 	-2.226668 	-0.057444 	0.949545 	-3.691023 	-0.145110 	-3.976962 	-6.565386 	-1.887831 	-3.299876 	5.403573 	0.000309 	-6.536948 
  9.000000 	0.593065 	-1.595681 	1.901661 	-2.061537 	2.873751 	5.135193 	-4.964619 	5.035066 	-4.387039 	3.366342 	-0.951675 	-2.412514 	-0.444232 	2.886478 	-0.085504 	1.423303 	-0.405195 	3.777356 	0.148127 	1.222258 	0.765240 	1.615988 	1.187308 	0.928989 	-5.600044 	-0.695803 	0.303103 	-2.272445 	5.183665 	2.245700 	-3.014688 	3.311362 	4.938635 	3.700982 	1.687659 	-4.162208 	-4.831276 	3.093513 	0.878138 	5.218243 	5.483579 	5.981500 	-1.638375 	-3.095071 	3.050526 	1.687013 	-5.034580 	9.969944 	5.810403 	-2.191712 	-1.555812 	-0.046359 	-2.298685 	-0.060767 	0.969778 	-3.625765 	-0.143847 	-3.931170 	-6.458547 	-1.903813 	-3.338418 	5.466458 	-0.030595 	-6.502999 
  10.000000 	0.833648 	-2.078650 	1.730803 	-2.099981 	2.751120 	5.153341 	-5.036942 	4.884516 	-4.793866 	3.081908 	-0.687072 	-2.371595 	-0.554149 	2.877865 	0.129862 	1.295595 	-1.023696 	4.740718 	0.210996 	1.270407 	1.133032 	1.264548 	1.184997 	0.843416 	-5.408689 	0.016483 	0.664747 	-2.549510 	4.747691 	2.623861 	-2.711944 	2.964114 	5.239022 	3.136874 	1.735691 	-4.290515 	-4.791953 	3.093722 	0.883315 	5.790473 	5.637001 	6.125669 	-1.783045 	-3.470497 	2.115491 	2.129422 	-5.158652 	10.153342 	6.353292 	-2.252300 	-2.130471 	-0.675499 	-2.410670 	0.889678 	0.783344 	-2.699044 	0.178616 	-3.617609 	-5.923612 	-1.683659 	-2.527237 	4.371545 	0.719371 	-7.126516 
  ```

  

- /data2/syd_data/Breakfast_Data/segmentation_coarse/s1_label/cereals/路径下的原始段级别标签数据格式为

  ```txt
  1-30 SIL  
  31-150 take_bowl  
  151-428 pour_cereals  
  429-575 pour_milk  
  576-705 stir_cereals  
  706-836 SIL  
  ```

采取`s1,s2,s3`文件夹中所有10个任务目标及其标签作为训练数据，`s4`及其标签作为测试数据。





## 2. 学术方案

**核心目标**: 验证通过一个小型神经网络$ (MLP_{diff}) $动态地、实时地调整任务图的转移概率，是否能够比使用固定的静态任务图更准确地预测程序性任务中的下一步动作。

**核心思路**: 构建并比较两个直接进行下一动作预测的模型：

- **模型 A (静态基线)**: 仅使用从训练数据中统计出的静态任务图$G_0$来预测下一动作。
- **模型 B (动态方法)**: 在静态图$G_0$的基础上，根据当前动作的“执行质量”（通过实际特征与原型的偏差$Diff_k$体现）实时生成一个调整量$ ΔW_k$，并用调整后的图来预测下一动作。



### 2.1 原型特征的训练

- 原理：对训练集（ $\mathrm{s} 1, \mathrm{~s} 2, \mathrm{~s} 3$ 文件夹下）中所有属于同一动作类别的视觉特征进行平均化处理，以计算出代表该类别＂标准外观＂的原型特征 $\mathcal{V}$ 

- 操作流程：在`/data2/syd_data/Breakfast_Data/Code/Train/`路径下新建一个
  `Train_Action_Prototype.py `的文件。该文件负责读取训练集中所有 .npy 文件的内容，并根据其对应的动作标签，对属于同一类别的所有视觉特征向量求取均值，最终生成并保存原型特征文件`action_prototypes.pt`

- 数学表达：

  - $N$ 为数据集中所有动作类别的集合，$|N|=M$ 为类别总数。
  - $n_i$ 为一个具体的动作类别，其中 $n_i \in N$ 。
  - $S_i=\left\{V_k \mid \operatorname{type}\left(a_k\right)=n_i\right\}$ 为训练集中所有类别为 $n_i$ 的视觉特征向量 $V_k$ 的集合。
  - $\left|S_i\right|$ 为集合 $S_i$ 中向量的总数量。

  那么，动作类别 $n_i$ 的原型特征 $\mathcal{V}_{n_i}$ 的计算公式为所有同类向量的算术平均值：

  $$
  \mathcal{V}_{n_i}=\frac{1}{\left|S_i\right|} \sum_{V_k \in S_i} V_k
  $$



### 2.2 任务图边权的训练

- 原理：统计s1,s2,s3文件夹下，训练集中所有相邻动作的转移频次，计算平滑后的条件概率 $P\left(n_j \mid n_i\right)$ ，并取对数得到初始的边权重$logits W_{i j}^0$ 

- 操作流程：在`/data2/syd_data/Breakfast_Data/Code/Train/`路径下新建一个Train_Edge_Weight.py的文件，在该文件中编写任务图边权训练的代码，并生成初始任务图的权重`edge_weights.pt并同时导出 edge_weights.csv（M×M）便于人工查看。`

- **数学表达：**
  - $N$ 为数据集中所有动作类别的集合，$|N|=M$ 为类别总数。
  - $C_{i j}$ 为训练集中从动作 $n_i \in N$ 转移到动作 $n_j \in N$ 的原始频次计数。
  - $\alpha$ 为平滑因子（Laplace/Add－one平滑中，$\alpha=1$ ）。
  - 那么，初始任务图 $G_0$ 中，从节点 $i$ 指向节点 $j$ 的边权重 $W_{i j}^0$ 的计算公式为：

$$
W_{i j}^0=\log \left(P\left(n_j \mid n_i\right)\right)=\log \left(\frac{C_{i j}+\alpha}{\sum_{k=1}^M\left(C_{i k}+\alpha\right)}\right)=\log \left(\frac{C_{i j}+\alpha}{\left(\sum_{k=1}^M C_{i k}\right)+M \alpha}\right)
$$




### 2.3 静态任务图的训练

- **原理：** 以预先计算好的静态任务图 G0 为唯一信息源，训练一个基线模型来预测下一动作。该模型通过最小化预测与真实下一动作的交叉熵损失，为后续的动态模型提供一个性能对比基准。

- **操作流程：** 在 `/data2/syd_data/Breakfast_Data/Code/Train/` 路径下新建一个 `Train_Static_Model.py` 文件。该文件加载`2.2 任务图边权的训练`中生成的静态图边权重 $W^0$，并使用训练集（s1, s2, s3）进行训练。训练的目标是最小化模型基于$W^0$所做的下一动作预测与真实下一动作之间的交叉熵损失。并生成静态任务图的偏置`static_model_bias.pt`

- **训练样本构建**: 为了让模型专注于学习**动作之间的转移**，我们不使用视频中的每一帧进行训练。训练样本仅在动作段的边界处生成。具体来说，对于一个动作序列，如果第 $k$ 个动作段（类别为 $n_k$）结束，并紧接着开始第 $k+1$ 个动作段（类别为 $n_{k+1}$），我们将构建一个训练样本：

  - **输入特征 V_k**: 使用第 $k$ 个动作段的**最后一帧**的视觉特征
  - **当前动作 n_k**: 第 $k$ 个动作段的标签
  - **目标标签 n_{k+1}**: 第 $k+1$ 个动作段的标签

  所有不发生动作切换的帧都将被忽略

- **数学表达：**
  - $W^0$ 为`2.2 任务图边权的训练`中计算出的、在训练中保持冻结的静态任务图边权重矩阵
  - $n_k$ 为序列中第 $k$ 步的真实动作类别。
  - $n_{k+1}$ 为序列中第 $k+1$ 步的真实动作类别。
  - $b$ 为一个小的、可学习的偏置向量（bias vector），其维度与动作类别总数 $M$ 相同。

在训练的每一步 $k$ ，模型进行如下计算：
1．计算 Logits：模型预测的下一动作的原始分数（Logits）由静态图权重和偏置向量相加得到：
$$
\operatorname{logits}_k=W_{n_k, *}^0+b
$$

（其中 $W_{n_k, *}^0$ 代表矩阵 $W^0$ 中对应动作 $n_k$ 的那一行）

2．计算损失：模型的损失函数 $\mathcal{L}_k$ 为该步骤预测的 Logits 与真实下一动作 $n_{k+1}$ 之间的交叉熵损失：- 
$$
\mathcal{L}_k=\operatorname{CrossEntropy}\left(\operatorname{logits}_k, n_{k+1}\right)
$$

3．优化目标：整个训练过程的优化目标是找到最优的偏置向量 $b^*$ ，以最小化在整个训练集上的总损失。注意，此过程中仅更新 $b, W^0$ 始终不变。
$$
b^*=\arg \min _b \sum_{\text {所有训练序列 }} \sum_k \mathcal{L}_k
$$


### 2.4 动态任务图的训练

- 原理：训练一个小型神经网络 $M L P_{\text {diff }}$ ，使其能够根据当前动作的实时执行质量 （通过视觉特征与原型的偏差来量化），动态地生成对静态任务图转移概率的调整量。最终目标是通过这种自适应调整，让模型更准确地预测下一动作。

- 操作流程：在`/data2/syd_data/Breakfast_Data/Code/Train/`路径下新建一个` Train_Dynamic_Model.py` 文件。该文件加载2．1节和2．2节中生成的原型特征 $\mathcal{V}$ 和静态图边权重 $W^0$ 。然后，使用训练集（ $\mathrm{s} 1, \mathrm{~s} 2, \mathrm{~s} 3$ ）训练 $M L P_{\text {diff }}$ 模型。训练的核心是最小化由静态图和动态调整量共同决定的预测结果与真实下一动作之间的交叉熵损失。并生成`dynamic_model_mlp.pt(该文件是完整 state_dict 而非仅权重矩阵，加载需调用同名网络结构。)`

- **训练样本构建**: 为了让模型专注于学习**动作之间的转移**，我们不使用视频中的每一帧进行训练。训练样本仅在动作段的边界处生成。具体来说，对于一个动作序列，如果第 $k$ 个动作段（类别为 $n_k$）结束，并紧接着开始第 $k+1$ 个动作段（类别为 $n_{k+1}$），我们将构建一个训练样本：

  - **输入特征 V_k**: 使用第 $k$ 个动作段的**最后一帧**的视觉特征
  - **当前动作 n_k**: 第 $k$ 个动作段的标签
  - **目标标签 n_{k+1}**: 第 $k+1$ 个动作段的标签

  所有不发生动作切换的帧都将被忽略


**数学表达**

-  $\mathcal{V}$ 为2．1节中计算出的动作原型特征，训练中保持冻结。
- $W^0$ 为2．2节中计算出的静态图权重，训练中保持冻结。
- $M L P_{\text {diff }}$ 为待训练的差分网络，其可学习参数为 $\theta$ 。
- $V_k$ 为序列中第 $k$ 步的视觉特征。
- $n_k$ 和 $n_{k+1}$ 分别为第 $k$ 步和第 $k+1$ 步的真实动作类别。

在训练的每一步 $k$ ，模型进行如下计算：
1．计算执行偏差：

$$
D i f f_k=\left|V_k-\mathcal{V}_{n_k}\right|
$$


2．计算权重调整量：

$$
\Delta W_k=M L P_{\operatorname{diff}}\left(D i f f_k ; \theta\right)
$$


3．计算 Logits：

$$
\operatorname{logits}_k=W_{n_k, *}^0+\Delta W_k
$$


4．计算损失：

$$
\mathcal{L}_k=\operatorname{CrossEntropy}\left(\operatorname{logits}_k, n_{k+1}\right)
$$
5．优化目标：整个训练过程的优化目标是找到 $M L P_{\mathrm{diff}}$ 的最优参数 $\theta^*$ ，以最小化在整个训练集上的总损失。注意，此过程中仅更新 $\theta, W^0$ 和 $\mathcal{V}$ 始终不变。

$$
\theta^*=\arg \min _\theta \sum_{\text {所有训练序列 }} \sum_k \mathcal{L}_k
$$

- $MLP_{diff}$架构：

- 输入层：维度与视觉特征$V_k$的维度相同。
- 隐藏层：包含两个全连接层，神经元数量分别为 512 和 256 。
- 激活函数：在隐藏层之间使用 ReLU 激活函数。
- 输出层：维度与动作类别总数 M 相同。不使用激活函数，因为其输出是直接加到 logits 上的调整量 $\Delta \mathrm{W}_k$ 



### 2.5 推理测试

在`/data2/syd_data/Breakfast_Data/Code/Test/`路径下编写Test_Static.py；Test_Dynamic.py；Test_Static_vs_Dynamic.py这三个python文件。

- Test_Static.py：在此文件中读取训练好的静态任务图的模型参数（`/data2/syd_data/Breakfast_Data/Outputs/Static/Model_parameters/`），加载**真实的标签序列**和模型保存的**原始预测序列**，然后计算所有核心评估指标（IoU, Accuracy等），并生成图表

- Test_Dynamic.py：在此文件中读取训练好的动态任务图的模型参数（`/data2/syd_data/Breakfast_Data/Outputs/Dynamic/Model_parameters/`），加载**真实的标签序列**和模型保存的**原始预测序列**，然后计算所有核心评估指标（IoU, Accuracy等），并生成图表
- Test_Static_vs_Dynamic.py：在此文件中读取静态任务图和动态任务图的模型参数，加载**真实的标签序列**和模型保存的**原始预测序列**，对比2者的差别。对每个指标做 **bootstrap 500 次** (按视频级重抽)，报告均值 ±95% CI，并写入 `evaluation_results/bootstrap_ci.csv`

推理测试的评估结果存储路径：`见5.4 推理测试的存储`





## 实验环境与评估指标

### 3.1 实验环境

- **硬件**:
  - **GPU**: NVIDIA RTX A6000 (48GB)

- **软件**:
  - **操作系统**: Linux (Ubuntu 18.04+)
  - **CUDA 版本**: 11.8
  - **Python 版本**: 3.8+
  - **核心框架**:
    - `pytorch==2.0.0`
    - `pytorch-lightning==2.2.0`
    - `hydra-core==1.3.0`




### 3.2 核心评估指标

除点估计外，所有指标同步报告 **95 % CI（bootstrap 500 次，视频级重采样）**。

**时序段级IoU (Temporal IoU)**

- 定义：衡量预测的动作片段在时间上的定位精度。我们报告在不同IoU阈值 $\kappa \in\{0.5,0.75,0.9\}$ 下的准确率，通常表示为 mIoU＠$\kappa$ 。采用 **片段级 micro IoU**（整合所有动作段后一次性累计 TP/FP/FN）。计算时排除 SIL 类别。

- 公式：对于一个真实的动作片段 $S_{g t}$ 和预测的片段 $S_p$ ，其IoU计算如下：

$$
\operatorname{IoU}\left(S_p, S_{g t}\right)=\frac{\left|S_p \cap S_{g t}\right|}{\left|S_p \cup S_{g t}\right|}
$$

**帧级准确度 (Frame-wise Accuracy)**

- 定义：计算被正确分类的帧数占视频总帧数的比例。评估阶段先将连续 `SIL` 合并为单段，并从 IoU / 编辑距离 / 覆盖率统计中 **移除 `SIL`**，$T$仅统计非 SIL 帧数。
- 公式：

$$
\text { Accuracy }=\frac{1}{T} \sum_{t=1}^T \mathbb{I}\left(\hat{y}_t=y_t\right)
$$


其中 $T$ 是总帧数，$y_t$ 和 $\hat{y}_t$ 分别是第 $t$ 帧的真实和预测标签， $\mathbb{I}(\cdot)$ 是指示函数。





**归一化编辑距离 (Normalized Levenshtein Distance)**

- 定义：从序列结构层面评估预测的动作序列与真实序列的相似度，值越低越好。公式：

  $$
  \text{Norm\_Edit\_Dist} = \frac{\text { Levenshtein }(P, G)}{|G|}
  $$

  其中，$P$ 和 $G$ 分别是预测和真实的动作段序列，Levenshtein $(\cdot)$ 是计算最少编辑操作（插入、删除、替换）的函数。比较前统一转为小写；连续 `sil` 合并后再计算。

  

**动作覆盖率 (Action Coverage)**

- 定义：衡量模型能够成功识别出的独一无二的动作类别占所有真实动作类别总数的比例。
- 公式：

$$
\text { Coverage }=\frac{\left|A_p \cap A_{g t}\right|}{\left|A_{g t}\right|}
$$


其中 $A_p$ 和 $A_{g t}$ 分别是预测和真实的独一无二动作类别集合。



## 4. 图表与报告生成规范

本章节定义了在实验过程中需要记录的核心数据，以及在最终报告和论文中需要呈现的关键图表，旨在清晰、有力地论证本研究的核心贡献。

### 4.1 数据（均以原始数据的形式保存，方便后续生成图表和分析）

- **原型特征、边权、静态任务图、动态任务图**的训练过程需要有可读性强并且可以直接用来生成可视化图表的数据
- 原型特征需要有：
  - **每个类别的样本数**: 即计算每个原型时，平均了多少个视觉特征向量$ (|S_i|)$
  - **每个类别的内部方差**: 计算每个动作类别内部，所有视觉特征向量与其均值（即原型）之间的平均距离或方差

- 边权需要有：
  - 原始转移频次矩阵 $\left(C_{i j}\right)$ ：记录从动作转移到动作的原始计数。这是最基础的统计数据
  - 条件概率矩阵 $\left(P\left(n_j \mid n_i\right)\right)$ ：在取对数之前的、经过平滑处理的条件概率矩阵

- 静态任务图需要有：
  - **训练/测试损失曲线**: 记录每个epoch（训练轮次）的训练损失（Training Loss）和测试损失（Validation Loss）
  - **最终模型参数**: 也就是训练好的那个小小的偏置向量
  - **在测试集上的完整预测序列**: 对于测试集（s4）里的每一个视频，都需要保存模型生成的从头到尾的动作标签序列。这是计算所有最终评估指标（IoU, Accuracy, Edit Distance等）的**原始数据**

- 动态任务图需要有：
  - **训练/测试损失曲线**: 记录每个epoch（训练轮次）的训练损失（Training Loss）和测试损失（Validation Loss）
  - 最终模型参数：即训练好的 $M L P_{\text {diff }}$ 的所有权重
  - **在测试集上的完整预测序列**: 对于测试集（s4）里的每一个视频，都需要保存模型生成的从头到尾的动作标签序列。这是计算所有最终评估指标（IoU, Accuracy, Edit Distance等）的**原始数据**
  - **每个测试样本的偏差 Diff_k 和调整量 ΔW_k**: 对于测试集中的关键帧（即动作转换帧），保存其 Diff_k 和 MLP_diff 输出的 ΔW_k。这对于后续分析 “什么样的执行偏差会导致什么样的图谱调整” 至关重要，是动态模型有效性的直接证据

数据存储路径见：`5. 输出与结果存储规范`



### 4.2 图表

- 原型特征、边权、静态任务图、动态任务图需要读取4.1中生成的原始数据生成对应的可视化图

可视化图表存储路径见：`5. 输出与结果存储规范`



**可视化标准:**

- **库**: 默认使用 `seaborn` 和 `matplotlib`。
- **风格**: 使用 `seaborn` 的 `whitegrid` 主题 (`sns.set_theme(style="whitegrid")`)
- **质量**: 所有保存的图表（如损失曲线、混淆矩阵等），必须设置为 `dpi=300`，并包含清晰的标题和坐标轴标签。
- **实时监控**: 使用 `TensorBoard` 可视化训练过程，日志需分别存放在各自方法的 `_logs` 文件夹内。





## 5. 输出与结果存储规范

### 5.1 模型参数的存储

- 动作原型的参数保存到`/data2/syd_data/Breakfast_Data/Outputs/Action_Prototype/Model_parameters/`路径下
- 初始图权重存储到`/data2/syd_data/Breakfast_Data/Outputs/Edge_Weight/Model_parameters/`路径下
- 静态任务图的模型参数存储到`/data2/syd_data/Breakfast_Data/Outputs/Static/Model_parameters/`路径下

- 动态任务图的模型参数存储到`/data2/syd_data/Breakfast_Data/Outputs/Dynamic/Model_parameters/`路径下



### 5.2 训练过程的原始数据存储

- 动作原型在训练过程中的详细数据存储到`/data2/syd_data/Breakfast_Data/Outputs/Action_Prototype/Raw_data/`路径下

- 初始图训练过程中的详细数据存储到`/data2/syd_data/Breakfast_Data/Outputs/Edge_Weight/Raw_data/`路径下
- 静态任务图训练过程中的详细数据存储到`/data2/syd_data/Breakfast_Data/Outputs/Static/Raw_data/`路径下

- 动态任务图训练过程中的详细数据存储到`/data2/syd_data/Breakfast_Data/Outputs/Dynamic/Raw_data/`路径下



### 5.3 图表的存储

- 原型特征的训练过程必要的可视化图存储到`/data2/syd_data/Breakfast_Data/Outputs/Action_Prototype/Visualization/`路径下
- 边权的训练过程必要的可视化图表存储到`/data2/syd_data/Breakfast_Data/Outputs/Edge_Weight/Visualization/`路径下
- 静态任务图的训练过程中必要的可视化图表存储到`/data2/syd_data/Breakfast_Data/Outputs/Static/Visualization/`路径下
- 动态任务图的训练过程中必要的可视化图表存储到`/data2/syd_data/Breakfast_Data/Outputs/Dynamic/Visualization/`路径下



### 5.4 推理测试的存储

- 推理测试的存储路径：`/data2/syd_data/Breakfast_Data/Outputs/Evaluation_Result/`

- 所有评估结果（包括点估计和置信区间）统一写入一个CSV文件：evaluation_summary_{时间戳ID}.csv。该文件应包含以下列：model_name (e.g., 'Static', 'Dynamic'), metric_name (e.g., 'Accuracy', 'mIoU@0.5'), point_estimate, ci_lower_bound, ci_upper_bound



## 备注

- 为了确保所有实验产物的可追溯性，所有由脚本自动生成的文件（包括模型参数、原始数据、图表、评估结果等）都必须遵循统一的命名规范。

- **核心原则**：每个文件的名称都应包含一个在**单次完整实验运行开始时**生成的、全局唯一的**时间戳ID**。

- **标准格式**:
  {基准名}_{时间戳ID}.{后缀}

  - **{基准名}**: 描述文件内容的核心名称，例如 loss_history, dynamic_model_mlp

  - **{时间戳ID}**: 采用 YYYYMMDD-HHMMSS 格式，例如 20250617-153005。这个ID在一次完整的实验流程（从数据预处理到最终评估）中应保持不变。

  - **{后缀}**: 文件的扩展名，例如 .pt, .csv, .png。



### 超参数

- 默认 max_epoch=10，early-stop关闭
- 随机种子42

- batch_size根据A6000调整











































































