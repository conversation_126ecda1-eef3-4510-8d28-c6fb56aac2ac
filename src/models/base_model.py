"""基础模型类"""
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseModel(nn.Module, ABC):
    """基础模型抽象类"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
    
    @abstractmethod
    def forward(self, *args, **kwargs):
        pass
    
    def save_model(self, filepath: str):
        """保存模型"""
        torch.save(self.state_dict(), filepath)
    
    def load_model(self, filepath: str):
        """加载模型"""
        self.load_state_dict(torch.load(filepath))
    
    def get_num_parameters(self) -> Dict[str, int]:
        """获取模型参数统计"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'frozen_parameters': total_params - trainable_params
        }
    
    def freeze_parameters(self, parameter_names: list = None):
        """冻结指定参数"""
        if parameter_names is None:
            # 冻结所有参数
            for param in self.parameters():
                param.requires_grad = False
        else:
            # 冻结指定参数
            for name, param in self.named_parameters():
                if any(pname in name for pname in parameter_names):
                    param.requires_grad = False
    
    def unfreeze_parameters(self, parameter_names: list = None):
        """解冻指定参数"""
        if parameter_names is None:
            # 解冻所有参数
            for param in self.parameters():
                param.requires_grad = True
        else:
            # 解冻指定参数
            for name, param in self.named_parameters():
                if any(pname in name for pname in parameter_names):
                    param.requires_grad = True
